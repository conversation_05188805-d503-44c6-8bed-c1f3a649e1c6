const fs = require('fs');

fs.copyFileSync('dist/js/main.js', 'dist/index.js');
fs.copyFileSync('dist/css/main.css', 'dist/index.css');
/**
 *   scripts: [
    {
      src: 'https://g.alicdn.com/code/lib/react/18.2.0/umd/react.production.min.js',
    },
    {
      src: 'https://g.alicdn.com/code/lib/react-dom/18.2.0/umd/react-dom.production.min.js',
    },
  ],
 */
// 通用函数：为HTML文件插入React相关脚本
const insertReactScripts = (htmlPath) => {
  const html = fs.readFileSync(htmlPath, 'utf8');
  const htmlWithScripts = html.replace(
    '</head>',
    `<script src="https://g.alicdn.com/code/lib/react/18.2.0/umd/react.production.min.js"></script>
  <script src="https://g.alicdn.com/code/lib/react-dom/18.2.0/umd/react-dom.production.min.js"></script>
  </head>`,
  );
  fs.writeFileSync(htmlPath, htmlWithScripts);
};

// 为多个页面插入脚本
['business-news', 'material-collection'].forEach((page) => {
  insertReactScripts(`dist/${page}.html`);
});
