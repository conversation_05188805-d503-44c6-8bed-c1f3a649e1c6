import { getField } from '../handle-data/datas';
import { IBusinessBaseData, NewBusinessNewsType } from '../const';
import { formatCompareValue } from '@/common/utils';
import { TASK_LABEL_ENUM } from '@/common/const';
import dayjs from 'dayjs';
import { domToImg } from '@/utils/domToImg';
import { fixed2 } from '@/utils';

export interface ITask {
  title: string;
  status: number;
}

const breakLine = '<br><br>';
export const getSummary = (
  data: IBusinessBaseData,
  businessNewType: NewBusinessNewsType,
  formData: any,
): string => {
  const { startDate, endDate, shopList = [] } = formData || {};
  const getFieldValue = (field: Parameters<typeof getField>['0']) => {
    return getField(field, data.dataResult, businessNewType === NewBusinessNewsType.FOOD);
  };
  const getValue = (
    field: Parameters<typeof getField>['0'],
    unit = '次',
    alias: string = field,
  ) => {
    const value = getFieldValue(field).value as null | string;
    return `<b>${value || '-'}</b> ${unit}${alias}`;
  };
  const getPercent = (field: Parameters<typeof getField>['0']) => {
    let { compareValue } = getFieldValue(field);
    if (compareValue && compareValue !== '-') {
      compareValue = fixed2(Number(compareValue));
    }
    if (compareValue !== undefined && compareValue != null) {
      const [compareColor, arrow, parsedCompareValue] = formatCompareValue(compareValue);
      // 环比为空和'--'文案都不展示环比
      if (parsedCompareValue === '--') {
        return '';
      }
      return `（<span style="color: ${compareColor};">${arrow}${parsedCompareValue}</span>）`;
    }
    return '';
  };
  const getValueAndPercent = (
    field: Parameters<typeof getField>['0'],
    unit = '次',
    alias: string = field,
  ) => {
    return `${getValue(field, unit, alias)} ${getPercent(field)}`;
  };
  // 获取策略建议:
  const getSuggestion = () => {
    let res = '';
    const labelList = data.strategySuggestDTO?.parentAdLabelList || [];
    const canSupportDays = data.strategySuggestDTO?.balanceConsumable || '-';
    const restAmount = data.strategySuggestDTO?.cashBalance || '-';
    const stopDays = data.strategySuggestDTO?.consecutiveNotAdvertising || '-';
    if (
      labelList.includes(TASK_LABEL_ENUM.应充预警) ||
      labelList.includes(TASK_LABEL_ENUM.停投召回)
    ) {
      res += `
      <blockquote>广告建议: </blockquote>
      <br>
      `;
      if (labelList.includes(TASK_LABEL_ENUM.应充预警)) {
        res += `
        <b>续充: </b> 当前您的余额还有${restAmount}元，推广计划预计仅够消耗${canSupportDays}天，为了不影响您的门店收入，请尽快充值
        `;
      }
      if (labelList.includes(TASK_LABEL_ENUM.停投召回)) {
        res += `
        <br>
        <b>投放: </b> 您已停投${stopDays}天, 为了不影响您的门店收入, 建议您尽快开启投放计划
        `;
      }
      res += breakLine;
    }
    return res;
  };
  const getAdPlan = () => {
    const shops = (data.strategySuggestDTO?.shopAdPlanConfigs || []).filter(
      (item) => item?.adPlanConfigs?.length,
    );
    let res = '';
    shops.forEach((shop) => {
      const shopName = shopList.find((item) => item.value === shop.shopId)?.label;
      if (!shopName) return;
      if (!res) {
        res += `
        <b>提出价&提预算: </b>
        ${breakLine}
        `;
      }
      res += `
      ${shopName}
      <ul>
        ${shop.adPlanConfigs
          .map((plan) => {
            return `<li>${plan.productName}: 建议出价${plan.offerSuggestion || '-'}元，日预算${
              plan.dailyBudgetSuggestion || '-'
            }元，${plan.effectPrediction || ''}</li>`;
          })
          .join('<br>')}
      </ul>
      `;
    });
    if (res) {
      res += breakLine;
    }
    return res;
  };
  const getDeadLine = () => {
    const annualExpirationDate = data.strategySuggestDTO?.annualExpirationDate;
    const deadline = annualExpirationDate
      ? dayjs(annualExpirationDate).format('YYYY年MM月DD日')
      : '';
    const expirationShopNum = data.strategySuggestDTO?.annualRenewalShopNum;
    if (deadline && expirationShopNum) {
      if (expirationShopNum > 1) {
        return `<b>年费续签: </b> <b>${expirationShopNum}</b> 家门店, 年费于 ${deadline} 将陆续到期, 请联系您的运维专员续签。`;
      } else {
        return `<b>年费续签: </b>您的门店, 年费于 <b>${deadline}</b> 到期, 请联系您的运维专员续签。`;
      }
    }
    return '';
  };
  let result = `
  <strong> 在 ${startDate.format('YYYY年MM月DD日')} 到 ${endDate.format(
    'YYYY年MM月DD日',
  )} 期间, 您的店铺经营情况如下:  </strong><br>`;

  /**
   * 美食板块
   */
  if (businessNewType === NewBusinessNewsType.FOOD) {
    result += `
    在高德地图上, 获得了 ${getValueAndPercent('曝光量')}、 ${getValueAndPercent('访问量')}
    <br>在高德地图上, 获得了 ${getValue('预约到店量', '个')}, 其中, ${getValue(
      '电话预订量',
      '个',
    )}、 ${getValue('在线总预订量', '个')}、${getValue('团购总订单量', '个')}
    ${breakLine}您通过广告投放,共消耗 ${getValue('广告总消耗', '元', ' ')}${getPercent(
      '广告总消耗',
    )}, 有效到店量 ${getValueAndPercent(
      '有效到店量',
      '个',
      ' ',
    )}, 有效到店成本 ${getValueAndPercent('有效到店成本', '元/人', ' ')}
    ${breakLine}
    `;
  } else {
    /**
     * 其他板块
     */
    result += `在高德地图上，获得了 ${getValueAndPercent('导航搜索量')}， ${getValueAndPercent(
      '导航到店量',
    )}
    ${breakLine}在高德地图、支付宝、口碑上，共 ${getValueAndPercent(
      '成交订单数',
      '笔',
      '成交订单',
    )}，${getValue('订单客资', '个', '客资量')}，${getValue('新增评论数', '个')}${getPercent(
      '新增评论数',
    )}
    ${breakLine}您通过广告投放，共消耗 ${getValue('广告总消耗', '元', ' ')}${getPercent(
      '广告总消耗',
    )}，有效客资成本 ${getValueAndPercent('有效客资成本', '元/人')}，获得了 ${getValueAndPercent(
      '广告曝光量',
      '',
      '门店曝光量',
    )}，${getValueAndPercent('广告点击量')}，${getValueAndPercent(
      '有效客资量',
      '个',
    )}，很可惜您错失了 ${getValue('错失客资', '个', '客资')}${breakLine}
    
    `;
  }
  result += `
  ${getSuggestion()}
  ${getAdPlan()}
  ${getDeadLine()}
  ${getTaskTemplate(data?.taskSummary?.list || [])}
  `;
  return result;
};

/**
 * 数据汇总-话术模板
 */
export const getTaskTemplate = (taskList: any) => {
  if (taskList?.length > 0) {
    let taskHtml = '';
    for (let i = 0; i < taskList.length && i < 5; i++) {
      const task: ITask = taskList[i];
      taskHtml += `<li>${task.title}</li>`;
    }
    return `<strong>您的门店需要对以下几个方面进行优化：</strong><ol>${taskHtml}</ol>`;
  }
  return '';
};

export const downloadShopDetail = async () => {
  const tableDom = document.querySelector('#shop-detail-table table');
  const extra = document.getElementById('shop-detail-extra');
  if (!extra) {
    return domToImg({
      dom: tableDom,
      isUpload: true,
    });
  }
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.bottom = '0';
  container.style.left = '0';
  container.style.zIndex = '-1';
  container.style.backgroundColor = '#fff';
  container.appendChild(tableDom.cloneNode(true));
  container.appendChild(extra.cloneNode(true));
  document.body.appendChild(container);
  const res = await domToImg({
    dom: container,
    isUpload: true,
  });
  document.body.removeChild(container);
  return res;
};
