import { useState, useEffect, useCallback } from 'react';
import { queryUnfinishedOptWoosBizOrder } from '@/services/batch-submit';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

export function useBatchSubmit() {
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchBtnDisabled, setBatchBtnDisabled] = useState(false);

  // 查询未完结批量提报任务
  useEffect(() => {
    queryUnfinishedOptWoosBizOrder({}).then((res) => {
      setBatchBtnDisabled(res.result === true);
    });
    // 挂载时曝光埋点
    traceExp(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
  }, []);

  // 打开弹窗
  const openBatchModal = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
    setBatchModalVisible(true);
  }, []);

  // 关闭弹窗
  const closeBatchModal = useCallback(() => {
    setBatchModalVisible(false);
  }, []);

  return {
    batchModalVisible,
    batchBtnDisabled,
    openBatchModal,
    closeBatchModal,
  };
}
