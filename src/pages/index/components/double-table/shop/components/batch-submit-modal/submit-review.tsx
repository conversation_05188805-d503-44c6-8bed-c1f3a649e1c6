import React, { useEffect, useState, useCallback } from 'react';
import { Table, Button, Input, Checkbox, Space, message } from 'antd';
import { batchSubmitAndCreateEspOrder, queryShopCollectInfo } from '@/services/batch-submit';
import { IShopInfo, ITargetShopSetting } from '@/types/batch-submit';
import { traceClick, traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface ISubmitReviewProps {
  shops: IShopInfo[];
  onTargetShopChange: (settings: ITargetShopSetting[]) => void;
  onCancel: () => void;
  onPrev: () => void;
  onSubmit: (settings: ITargetShopSetting[]) => void;
}

const SubmitReview: React.FC<ISubmitReviewProps> = ({
  shops,
  onTargetShopChange,
  onCancel,
  onPrev,
  onSubmit,
}) => {
  const [settings, setSettings] = useState<ITargetShopSetting[]>([]);
  const [loading, setLoading] = useState(false);

  // 初始化目标门店设置，查询装修素材提报记录
  useEffect(() => {
    const fetchCollectNames = async () => {
      const newSettings: ITargetShopSetting[] = [];
      for (const shop of shops) {
        let collectShopName = '';
        try {
          const res = await queryShopCollectInfo({ shopId: shop.shopId });
          collectShopName = res.collectShopName || '';
        } catch {
          // ignore error
        }
        newSettings.push({
          shopId: shop.shopId,
          targetShopName: collectShopName,
          hasSubmitRecord: !!collectShopName,
          isNoTarget: !collectShopName,
        });
      }
      setSettings(newSettings);
      onTargetShopChange(newSettings);
    };
    fetchCollectNames();
    // eslint-disable-next-line
  }, [shops]);

  // 目标门店名称变更
  const handleTargetShopNameChange = useCallback(
    (shopId: string, value: string) => {
      setSettings((prev) => {
        const updated = prev.map((item) =>
          item.shopId === shopId ? { ...item, targetShopName: value, isNoTarget: !value } : item,
        );
        onTargetShopChange(updated);
        return updated;
      });
    },
    [onTargetShopChange],
  );

  // 无目标门店勾选变更
  const handleNoTargetChange = useCallback(
    (shopId: string, checked: boolean) => {
      setSettings((prev) => {
        const updated = prev.map((item) =>
          item.shopId === shopId
            ? { ...item, isNoTarget: checked, targetShopName: checked ? '' : item.targetShopName }
            : item,
        );
        onTargetShopChange(updated);
        return updated;
      });
    },
    [onTargetShopChange],
  );

  // 上一步埋点
  const handlePrevStep = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.取消'], { type: 'prev' });
    onPrev();
  }, [onPrev]);
  // 取消埋点
  const handleCancel = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.取消'], { type: 'cancel' });
    onCancel();
  }, [onCancel]);

  // 提交审核
  const handleSubmit = useCallback(async () => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.提交'], {});
    // 校验：每个门店必须填写目标门店或勾选无目标门店
    for (const item of settings) {
      if (!item.targetShopName && !item.isNoTarget) {
        message.warning('请为所有门店填写目标门店名称或勾选无目标门店');
        return;
      }
    }
    setLoading(true);
    try {
      await batchSubmitAndCreateEspOrder({
        createReqs: settings.map((item) => ({
          shopId: item.shopId,
          collectShopName: item.targetShopName || '',
        })),
      });
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提交成功'], {});
      message.success('批量提报任务创建成功');
      onSubmit(settings);
    } catch {
      message.error('批量提报任务创建失败');
    } finally {
      setLoading(false);
    }
  }, [settings, onSubmit]);

  // 表格列定义
  const columns = [
    {
      title: '门店名称',
      dataIndex: 'shopName',
      key: 'shopName',
      render: (_: any, record: IShopInfo) =>
        shops.find((s) => s.shopId === record.shopId)?.shopName,
    },
    { title: '门店ID', dataIndex: 'shopId', key: 'shopId' },
    {
      title: '商户ID',
      dataIndex: 'pid',
      key: 'pid',
      render: (_: any, record: IShopInfo) => shops.find((s) => s.shopId === record.shopId)?.pid,
    },
    {
      title: '最近提报时间',
      dataIndex: 'lastSubmitTime',
      key: 'lastSubmitTime',
      render: (_: any, record: IShopInfo) =>
        shops.find((s) => s.shopId === record.shopId)?.lastSubmitTime,
    },

    {
      title: '无目标门店',
      dataIndex: 'isNoTarget',
      key: 'isNoTarget',
      render: (_: any, record: IShopInfo) => {
        const setting = settings.find((s) => s.shopId === record.shopId);
        return (
          <Checkbox
            checked={!!setting?.isNoTarget}
            onChange={(e) => handleNoTargetChange(record.shopId, e.target.checked)}
          >
            无目标
          </Checkbox>
        );
      },
    },
    {
      title: '目标门店名称',
      dataIndex: 'targetShopName',
      key: 'targetShopName',
      render: (_: any, record: IShopInfo) => {
        const setting = settings.find((s) => s.shopId === record.shopId);
        return (
          <Input
            value={setting?.targetShopName}
            disabled={setting?.isNoTarget}
            onChange={(e) => handleTargetShopNameChange(record.shopId, e.target.value)}
            placeholder="请输入目标门店名称"
            style={{ width: 160 }}
          />
        );
      },
    },
  ];

  return (
    <div>
      <Table
        rowKey="shopId"
        columns={columns}
        dataSource={shops}
        pagination={false}
        scroll={{ x: 1000 }}
      />
      <div
        style={{
          marginTop: 24,
          textAlign: 'right',
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Space>
          <Button onClick={handlePrevStep}>上一步</Button>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            提报审核
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default SubmitReview;
