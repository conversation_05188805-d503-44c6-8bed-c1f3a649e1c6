/**
 * ESP工单提报模块
 *
 * 该模块用于门店基础设施运维的ESP工单提报功能，包括：
 * - 基于角色的提报权限控制（运维1/运维2）
 * - 商家质量分版本判断和延迟提示
 * - 质检结果展示和历史记录
 * - 无法提报原因收集和管理
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
import React, { useState, useMemo, useEffect } from 'react';
import { Input, Button, Flex, Form, Checkbox, Timeline, Spin } from 'antd';
import ReasonModal from '../../ReasonModal';
import AuditTypeTag from '../AuditTypeTag';
import ExpandableCard from '@/components/expandable-card';
import useModal from '@/hooks/useModal';
import { useRequest } from 'ahooks';
import {
  configBusinessNewsGrey,
  queryLocalOptEspWoosOrder,
  queryShopCollectInfo,
} from '@/services/index';
import { jumpExternalUrl } from '@/common/utils';
import { getUserRole, checkSubmitPermission } from '../../utils';
import InspectionResultInfo from '../InspectionResultInfo';
import { useSubmit } from './useSubmit';
import { traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface IProps {
  shopId: string;
}

const MerchantAuditForm: React.FC<IProps> = ({ shopId: _shopId }) => {
  // shopId = '2023102611077000000107979002|xiniu_xgc_bgc';
  const [form] = Form.useForm();
  const [expanded, setExpanded] = useState(true); // 卡片展开状态
  const [isFirstRequest, setIsFirstRequest] = useState(true); // 是否首次请求，用于控制自动收起逻辑

  const { openModal: showModal, modalProps } = useModal<{}, { shopId: string }>();

  const { data: isShow } = useRequest(
    async () => {
      const res = await configBusinessNewsGrey(['OPT_ESP_ORDER_CREATE_GREY']);
      return res?.[0]?.switchStatus;
    },
    {
      onSuccess: (data) => {
        if (data) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['基建任务.提报审核'], {
            shopId: _shopId,
          });
        }
      },
    },
  );

  const shopId = isShow ? _shopId : '';

  // 获取ESP工单审核数据
  const {
    data: auditData,
    loading,
    run: refreshAuditData,
  } = useRequest(
    async (auditId?: string) => {
      if (shopId) {
        const res = await queryLocalOptEspWoosOrder({ shopId, auditId });
        setIsFirstRequest(false);

        // 根据提交状态设置表单初始值
        if (!res.submitterId) {
          // 未提交过，尝试获取门店采买信息作为默认值
          const collectInfo = await queryShopCollectInfo(shopId);
          if (collectInfo?.collectShopName) {
            form.setFieldsValue({
              collectShopName: collectInfo?.collectShopName,
              hasNoMerchant: false,
            });
          } else {
            form.setFieldsValue({
              collectShopName: '',
              hasNoMerchant: false,
            });
          }
        } else if (res?.collectShopName) {
          // 已提交过且有目标门店名称
          form.setFieldsValue({
            collectShopName: res?.collectShopName,
            hasNoMerchant: false,
          });
        } else {
          // 已提交过但无目标门店
          form.setFieldsValue({
            collectShopName: '',
            hasNoMerchant: true,
          });
        }

        // 首次加载且已提交且非拒绝状态时自动收起卡片
        if (isFirstRequest && res?.submitterId && res?.auditStatus !== 'AUDIT_REJECT') {
          setExpanded(false);
        }
        return res;
      }
      return null;
    },
    {
      refreshDeps: [shopId],
    },
  );
  const hasNoMerchant = Form.useWatch('hasNoMerchant', form);
  const isRejection = auditData?.auditStatus === 'AUDIT_REJECT';

  // 角色判断和权限检查
  const userRole = useMemo(() => {
    return getUserRole(auditData?.shopStaffRelations);
  }, [auditData?.shopStaffRelations]);

  const submitPermission = useMemo(() => {
    const hasSubmitted = !!auditData?.submitterId;
    return checkSubmitPermission(userRole, hasSubmitted, auditData?.auditStatus);
  }, [userRole, auditData?.submitterId, auditData?.auditStatus]);

  const { handleSubmit, submitting } = useSubmit({
    shopId,
    form,
    submitPermission,
    isRejection,
    setExpanded,
    refreshAuditData,
    auditStatus: auditData?.auditStatus,
  });

  const isAuditPassed = auditData?.auditStatus === 'AUDIT_PASS';
  const disabledOperation = auditData?.submitterId && !isRejection && !isAuditPassed;
  const hasSubmitted = !!auditData?.submitterId;

  // 根据角色权限决定是否禁用提报按钮
  const isSubmitDisabled =
    !submitPermission.canSubmit || disabledOperation || !!auditData?.taskExecuteInfo;

  // 根据审核状态获取时间线颜色
  const getTimelineItemColor = (status: string) => {
    switch (status) {
      case 'AUDIT_PASS':
        return 'green';
      case 'AUDIT_REJECT':
        return 'red';
      default:
        return '#1a66ff';
    }
  };

  // 跳转到审核详情页面 - 用于质检结果信息中的查看详情
  const goToAuditDetail = () => {
    jumpExternalUrl(auditData.lastAuditInfo.optEspOrderDetailUrl);
  };

  // 跳转到当前审核详情页面 - 用于提交成功后的查看详情
  const goToCurrentAuditDetail = () => {
    jumpExternalUrl(auditData.optEspOrderDetailUrl);
  };

  // 渲染卡片操作区域
  const renderCardActions = () => {
    return (
      <Flex align="center" gap={10}>
        {hasSubmitted && (
          <div>
            审核状态：
            <span style={{ color: getTimelineItemColor(auditData?.auditStatus) }}>
              {auditData?.auditStatusDesc}
            </span>
          </div>
        )}

        {!disabledOperation && (
          <div
            style={{
              position: 'relative',
              textAlign: auditData?.taskExecuteInfo ? 'right' : 'center',
            }}
          >
            {expanded && (
              <Button
                loading={submitting}
                disabled={isSubmitDisabled}
                variant="solid"
                color="orange"
                onClick={handleSubmit}
              >
                提报审核
              </Button>
            )}

            {expanded && !auditData?.taskExecuteInfo && (
              <div
                onClick={showModal}
                style={{
                  fontSize: 12,
                  cursor: 'pointer',
                  userSelect: 'none',
                  position: 'absolute',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '100%',
                }}
              >
                无法提报?
              </div>
            )}
            {auditData?.taskExecuteInfo && (
              <div
                style={{
                  fontSize: 12,
                  color: '#ff4d4f',
                  marginTop: '4px',
                }}
              >
                {auditData.taskExecuteInfo}
              </div>
            )}
          </div>
        )}
        {hasSubmitted && <a onClick={goToCurrentAuditDetail}>查看详情</a>}
      </Flex>
    );
  };

  const onExpandChange = (val: boolean) => {
    setExpanded(val);
  };

  const lastOrder = auditData?.auditBizOrders?.[auditData?.auditBizOrders?.length - 1];

  if (!isShow) {
    return null;
  }

  return (
    <Spin spinning={loading}>
      <Form form={form} disabled={disabledOperation}>
        <ExpandableCard
          disabled={!hasSubmitted}
          expanded={expanded}
          onExpandChange={onExpandChange}
          title="提报审核"
          actions={[renderCardActions()]}
          style={{ margin: '8px 0' }}
          header={
            <div>
              <Flex align="center" justify="space-between" gap={24}>
                <Form.Item style={{ marginBottom: 0, flex: 3 }} label="目标门店">
                  <Input value={auditData?.collectShopName || '无目标门店'} disabled />
                </Form.Item>
                <Flex style={{ flexShrink: 0 }} gap={6}>
                  <span>{lastOrder?.gmtTime || ''}</span>
                  <span>{lastOrder?.orderNodeDesc || ''}</span>
                </Flex>
              </Flex>
            </div>
          }
        >
          <Flex style={{ maxHeight: 200, paddingTop: expanded && !disabledOperation ? 14 : 0 }}>
            <div style={{ flex: 2 }}>
              <Form.Item label="目标门店" name="collectShopName" style={{ marginBottom: 5 }}>
                <Input
                  placeholder={'请填写目标门店名称'}
                  disabled={hasNoMerchant || disabledOperation}
                />
              </Form.Item>
              <Form.Item name="hasNoMerchant" valuePropName="checked" style={{ marginBottom: 7 }}>
                <Checkbox>无目标门店</Checkbox>
              </Form.Item>

              <div>
                <p>1. 请务必确认目标门店名称准确无误</p>
                <p>2. 若无目标门店，请选择&quot;无目标门店&quot;</p>
              </div>

              {/* 显示上次质检结果信息 */}
              <InspectionResultInfo
                shopInspectionInfo={auditData?.shopItemInspectInfo}
                lastAuditInfo={auditData?.lastAuditInfo}
                auditStatus={auditData?.auditStatus}
                auditId={auditData?.auditId}
                onViewDetail={goToAuditDetail}
              />
            </div>
            {auditData?.auditBizOrders?.length ? (
              <div style={{ flex: 1, overflowY: 'auto', paddingTop: 10, marginLeft: 30 }}>
                <Timeline
                  items={(auditData?.auditBizOrders || []).map((record) => {
                    return {
                      color: getTimelineItemColor(record.orderNodeDesc),
                      children: (
                        <div key={record.gmtTime}>
                          <div>{record.gmtTime}</div>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span>{record.orderNodeDesc}</span>
                            {auditData?.orderAuditType &&
                              record.orderNodeDesc?.includes('提报审核') && (
                                <AuditTypeTag auditType={auditData.orderAuditType} />
                              )}
                          </div>
                        </div>
                      ),
                    };
                  })}
                />
              </div>
            ) : null}
          </Flex>
        </ExpandableCard>
      </Form>
      <ReasonModal {...modalProps} shopId={shopId} />
    </Spin>
  );
};

export default MerchantAuditForm;
